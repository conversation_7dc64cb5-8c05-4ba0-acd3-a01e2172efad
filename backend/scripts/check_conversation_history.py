#!/usr/bin/env python3
"""
Check conversation history for the user to understand the data source.
"""

import asyncio
import sys
import os
import json
from datetime import datetime, time, timedelta
from dotenv import load_dotenv

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Load environment variables
load_dotenv()

# Initialize Firebase
import firebase_admin
from firebase_admin import credentials, firestore

if not firebase_admin._apps:
    service_account_json = os.getenv('SERVICE_ACCOUNT_JSON')
    if service_account_json:
        service_account_info = json.loads(service_account_json)
        cred = credentials.Certificate(service_account_info)
        firebase_admin.initialize_app(cred)
        print("✅ Firebase initialized successfully")

import database.conversations as conversations_db


def check_conversation_history(user_id: str):
    """Check conversation history across different date ranges"""
    print(f"🔍 Checking Conversation History for User: {user_id}")
    print("=" * 80)
    
    # Check different date ranges
    today = datetime.now().date()
    date_ranges = [
        ("Today", today, today),
        ("Yesterday", today - timedelta(days=1), today - timedelta(days=1)),
        ("Last 7 days", today - timedelta(days=7), today),
        ("Last 30 days", today - timedelta(days=30), today),
    ]
    
    for range_name, start_date, end_date in date_ranges:
        print(f"\n📅 {range_name} ({start_date} to {end_date}):")
        print("-" * 50)
        
        try:
            conversations = conversations_db.filter_conversations_by_date(
                user_id,
                datetime.combine(start_date, time.min),
                datetime.combine(end_date, time.max)
            )
            
            print(f"📊 Found {len(conversations)} conversations")
            
            if conversations:
                # Show details of recent conversations
                for i, conv in enumerate(conversations[:3], 1):
                    created_at = conv.get('created_at')
                    if hasattr(created_at, 'strftime'):
                        time_str = created_at.strftime('%Y-%m-%d %H:%M:%S')
                    else:
                        time_str = str(created_at)
                        
                    print(f"  {i}. ID: {conv.get('id', 'N/A')}")
                    print(f"     Created: {time_str}")
                    print(f"     Title: {conv.get('structured', {}).get('title', 'N/A')}")
                    
                    # Check transcript segments
                    segments = conv.get('transcript_segments', [])
                    print(f"     Segments: {len(segments)}")
                    
                    if segments:
                        sample_text = ""
                        for seg in segments[:2]:
                            sample_text += seg.get('text', '') + " "
                        print(f"     Sample: {sample_text[:100]}...")
                    print()
                    
                if len(conversations) > 3:
                    print(f"  ... and {len(conversations) - 3} more conversations")
                    
        except Exception as e:
            print(f"❌ Error checking {range_name}: {e}")


def check_daily_summary_source():
    """Check where the daily summary content is actually coming from"""
    print(f"\n🔍 Investigating Daily Summary Source")
    print("=" * 80)
    
    user_id = "mcaK5709t3MZAcUpdAeEGrmYgaT2"
    
    # Check if there are any existing daily summary messages
    import database.chat as chat_db
    
    messages = chat_db.get_messages(uid=user_id, limit=20, app_id=None)
    daily_summaries = [msg for msg in messages if msg.get('type') == 'day_summary']
    
    print(f"📅 Found {len(daily_summaries)} existing daily summary messages")
    
    if daily_summaries:
        print(f"\n🔍 Analyzing existing daily summaries:")
        
        for i, summary in enumerate(daily_summaries[:3], 1):
            print(f"\n--- Daily Summary {i} ---")
            print(f"📄 ID: {summary.get('id')}")
            print(f"🕒 Created: {summary.get('created_at')}")
            print(f"📝 Content: {summary.get('text', '')}")
            print(f"📊 Content length: {len(summary.get('text', ''))} characters")
            
            # Analyze the content pattern
            content = summary.get('text', '')
            if content:
                lines = content.split('\n')
                numbered_items = [line for line in lines if line.strip() and (line.strip()[0].isdigit() if line.strip() else False)]
                
                print(f"📋 Analysis:")
                print(f"   - Total lines: {len(lines)}")
                print(f"   - Numbered items: {len(numbered_items)}")
                print(f"   - Language: {'Chinese' if any(ord(c) > 127 for c in content) else 'English'}")
                print(f"   - Format: {'Action items' if numbered_items else 'Narrative'}")
                
                # Check if content looks like it came from conversations vs templates
                if "评估并推动Noah模型" in content:
                    print(f"   - Content type: Work-related action items")
                    print(f"   - Likely source: Real conversation about work tasks")
                elif "TEST:" in content:
                    print(f"   - Content type: Test message")
                    print(f"   - Likely source: Testing/debugging")
                else:
                    print(f"   - Content type: Unknown")


def check_firebase_conversations_directly():
    """Check Firebase conversations collection directly"""
    print(f"\n🔍 Checking Firebase Conversations Collection Directly")
    print("=" * 80)
    
    user_id = "mcaK5709t3MZAcUpdAeEGrmYgaT2"
    
    try:
        db = firestore.client()
        user_ref = db.collection('users').document(user_id)
        conversations_ref = user_ref.collection('conversations')
        
        # Get recent conversations
        conversations = conversations_ref.order_by('created_at', direction=firestore.Query.DESCENDING).limit(10).stream()
        
        conv_list = []
        for doc in conversations:
            conv_data = doc.to_dict()
            conv_data['doc_id'] = doc.id
            conv_list.append(conv_data)
        
        print(f"📊 Found {len(conv_list)} conversations in Firebase")
        
        if conv_list:
            print(f"\n📋 Recent conversations:")
            for i, conv in enumerate(conv_list[:5], 1):
                created_at = conv.get('created_at')
                if hasattr(created_at, 'strftime'):
                    time_str = created_at.strftime('%Y-%m-%d %H:%M:%S')
                else:
                    time_str = str(created_at)
                    
                print(f"  {i}. Doc ID: {conv.get('doc_id')}")
                print(f"     Created: {time_str}")
                print(f"     Title: {conv.get('structured', {}).get('title', 'N/A')}")
                print(f"     Overview: {conv.get('structured', {}).get('overview', 'N/A')[:100]}...")
                
                # Check if this conversation has transcript data
                segments = conv.get('transcript_segments', [])
                print(f"     Transcript segments: {len(segments)}")
                
                if segments:
                    total_text = sum(len(seg.get('text', '')) for seg in segments)
                    print(f"     Total transcript text: {total_text} characters")
                print()
        else:
            print("❌ No conversations found in Firebase")
            
    except Exception as e:
        print(f"❌ Error checking Firebase directly: {e}")
        import traceback
        traceback.print_exc()


def main():
    """Main function"""
    user_id = "mcaK5709t3MZAcUpdAeEGrmYgaT2"
    
    print("🔍 Daily Summary Content Source Investigation")
    print("=" * 80)
    
    # Check conversation history across date ranges
    check_conversation_history(user_id)
    
    # Check existing daily summary messages
    check_daily_summary_source()
    
    # Check Firebase directly
    check_firebase_conversations_directly()
    
    print(f"\n📋 Investigation Summary")
    print("=" * 80)
    print("This analysis will help determine:")
    print("1. Whether conversations exist for this user")
    print("2. What date range contains conversation data")
    print("3. Whether daily summaries are generated from real conversations")
    print("4. Whether the 'todo list' format is intentional or a bug")


if __name__ == "__main__":
    main()
