#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to send daily summary notifications to all users immediately.
This bypasses timezone restrictions for testing purposes.

Usage:
    python scripts/send_daily_summary_to_all_users.py
    
    # Or to send to a specific user:
    python scripts/send_daily_summary_to_all_users.py --user-id USER_ID
"""

import asyncio
import sys
import os
import argparse
from unittest.mock import patch

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Load environment variables from .env file
from dotenv import load_dotenv
load_dotenv()

# Initialize Firebase
import firebase_admin
from firebase_admin import credentials
import json

if not firebase_admin._apps:
    # Initialize Firebase with service account
    service_account_json = os.getenv('SERVICE_ACCOUNT_JSON')
    if service_account_json:
        service_account_info = json.loads(service_account_json)
        cred = credentials.Certificate(service_account_info)
        firebase_admin.initialize_app(cred)
        print("✅ Firebase initialized successfully")
    else:
        print("❌ SERVICE_ACCOUNT_JSON not found in environment variables")

from utils.other.notifications import send_daily_summary_notification
import database.notifications as notification_db


async def send_to_all_users():
    """Send daily summary notifications to all users"""
    print("🚀 Sending daily summary notifications to ALL users")
    print("=" * 60)
    
    try:
        # Get all users by mocking timezone function to return common timezones
        with patch('utils.other.notifications._get_timezones_at_time') as mock_tz:
            # Return common timezones to catch most users
            mock_tz.return_value = [
                'UTC', 'America/New_York', 'America/Los_Angeles', 'America/Chicago',
                'Europe/London', 'Europe/Paris', 'Europe/Berlin', 'Europe/Madrid',
                'Asia/Tokyo', 'Asia/Shanghai', 'Asia/Kolkata', 'Asia/Seoul',
                'Australia/Sydney', 'America/Toronto', 'America/Mexico_City',
                'Europe/Rome', 'Europe/Amsterdam', 'Asia/Singapore',
                'Pacific/Auckland', 'America/Sao_Paulo'
            ]
            
            print("📤 Sending notifications...")
            result = await send_daily_summary_notification()

            # The original function returns None on success, so we check for exceptions instead
            print("✅ Success! Daily summary notifications sent to all users")
            print("💡 Users can now tap notifications to view daily summaries")
            return True
                
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False


async def send_to_specific_user(user_id: str):
    """Send daily summary notification to a specific user"""
    print(f"🎯 Sending daily summary notification to user: {user_id}")
    print("=" * 60)
    
    try:
        # Get user's token
        user_token = notification_db.get_token_only(user_id)
        if not user_token:
            print(f"❌ No FCM token found for user {user_id}")
            return False
        
        print(f"👤 User ID: {user_id}")
        print(f"🔑 Token: {user_token[:20]}...")
        
        # Mock functions to target this specific user
        with patch('utils.other.notifications._get_timezones_at_time') as mock_tz:
            with patch('database.notifications.get_users_id_in_timezones') as mock_users:
                mock_tz.return_value = ['UTC']
                mock_users.return_value = [(user_id, user_token)]
                
                print("📤 Sending notification...")
                result = await send_daily_summary_notification()

                # The original function returns None on success, so we check for exceptions instead
                print("✅ Success! Notification sent")
                print("💡 User can now tap notification to view daily summary")
                return True
                    
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False


async def list_users_with_tokens():
    """List users who have FCM tokens"""
    print("📋 Listing users with FCM tokens")
    print("=" * 60)
    
    try:
        # Get users from common timezones
        common_timezones = [
            'UTC', 'America/New_York', 'America/Los_Angeles', 'America/Chicago',
            'Europe/London', 'Europe/Paris', 'Europe/Berlin', 'Asia/Tokyo',
            'Asia/Shanghai', 'Asia/Kolkata', 'Australia/Sydney'
        ]
        
        users_with_ids = await notification_db.get_users_id_in_timezones(common_timezones)
        
        if users_with_ids:
            print(f"Found {len(users_with_ids)} users with FCM tokens:")
            for i, (uid, token) in enumerate(users_with_ids[:10], 1):  # Show first 10
                print(f"  {i}. User ID: {uid}, Token: {token[:20]}...")
            
            if len(users_with_ids) > 10:
                print(f"  ... and {len(users_with_ids) - 10} more users")
        else:
            print("No users with FCM tokens found")
        
        return users_with_ids
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return []


def main():
    """Main function"""
    parser = argparse.ArgumentParser(description='Send daily summary notifications')
    parser.add_argument('--user-id', help='Send to specific user ID')
    parser.add_argument('--list-users', action='store_true', help='List users with FCM tokens')
    parser.add_argument('--all-users', action='store_true', help='Send to all users (default)')
    
    args = parser.parse_args()
    
    print("📱 Daily Summary Notification Sender")
    print("=" * 60)
    
    if args.list_users:
        asyncio.run(list_users_with_tokens())
    elif args.user_id:
        asyncio.run(send_to_specific_user(args.user_id))
    else:
        # Default: send to all users
        asyncio.run(send_to_all_users())


if __name__ == "__main__":
    main()
