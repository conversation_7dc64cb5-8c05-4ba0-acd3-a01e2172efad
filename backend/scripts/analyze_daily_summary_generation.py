#!/usr/bin/env python3
"""
Comprehensive analysis of daily summary content generation pipeline.
This script traces the entire process from conversation data to final summary.
"""

import asyncio
import sys
import os
import json
from datetime import datetime, time
from dotenv import load_dotenv

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Load environment variables
load_dotenv()

# Initialize Firebase
import firebase_admin
from firebase_admin import credentials

if not firebase_admin._apps:
    service_account_json = os.getenv('SERVICE_ACCOUNT_JSON')
    if service_account_json:
        service_account_info = json.loads(service_account_json)
        cred = credentials.Certificate(service_account_info)
        firebase_admin.initialize_app(cred)
        print("✅ Firebase initialized successfully")

import database.conversations as conversations_db
from utils.llm.external_integrations import get_conversation_summary
from utils.llms.memory import get_prompt_memories
from models.conversation import Conversation


def analyze_conversation_data(user_id: str):
    """Analyze the raw conversation data for the user"""
    print(f"🔍 Step 1: Analyzing Raw Conversation Data")
    print("=" * 80)
    
    try:
        # Get today's conversations
        memories_data = conversations_db.filter_conversations_by_date(
            user_id, 
            datetime.combine(datetime.now().date(), time.min), 
            datetime.now()
        )
        
        print(f"📊 Found {len(memories_data)} conversations for today")
        
        if not memories_data:
            print("❌ No conversations found - this explains empty summaries")
            return None
            
        # Analyze each conversation
        for i, memory_dict in enumerate(memories_data[:3], 1):  # Show first 3
            print(f"\n--- Conversation {i} ---")
            print(f"📄 ID: {memory_dict.get('id', 'N/A')}")
            print(f"🕒 Created: {memory_dict.get('created_at', 'N/A')}")
            print(f"📝 Title: {memory_dict.get('structured', {}).get('title', 'N/A')}")
            print(f"📋 Overview: {memory_dict.get('structured', {}).get('overview', 'N/A')[:100]}...")
            
            # Check transcript segments
            segments = memory_dict.get('transcript_segments', [])
            print(f"🗣️  Transcript segments: {len(segments)}")
            
            if segments:
                # Show sample transcript content
                sample_text = ""
                for seg in segments[:3]:  # First 3 segments
                    sample_text += seg.get('text', '') + " "
                print(f"📝 Sample transcript: {sample_text[:150]}...")
            else:
                print("⚠️  No transcript segments found")
                
        return memories_data
        
    except Exception as e:
        print(f"❌ Error analyzing conversation data: {e}")
        import traceback
        traceback.print_exc()
        return None


def analyze_memory_context(user_id: str):
    """Analyze the user's memory context used in prompts"""
    print(f"\n🧠 Step 2: Analyzing User Memory Context")
    print("=" * 80)
    
    try:
        user_name, memories_str = get_prompt_memories(user_id)
        
        print(f"👤 User name: {user_name}")
        print(f"📚 Memory context length: {len(memories_str)} characters")
        print(f"📝 Memory context preview:")
        print(f"   {memories_str[:300]}...")
        
        return user_name, memories_str
        
    except Exception as e:
        print(f"❌ Error analyzing memory context: {e}")
        return None, None


def analyze_conversation_processing(user_id: str, memories_data: list):
    """Analyze how conversations are processed into Conversation objects"""
    print(f"\n🔄 Step 3: Analyzing Conversation Processing")
    print("=" * 80)
    
    try:
        # Convert dictionary data to Conversation objects (same as in notifications.py)
        memories = []
        for memory_dict in memories_data:
            try:
                memory = Conversation(**memory_dict)
                memories.append(memory)
                print(f"✅ Successfully converted conversation: {memory.id}")
            except Exception as e:
                print(f"❌ Error converting memory to Conversation object: {e}")
                print(f"   Memory dict keys: {list(memory_dict.keys())}")
                continue
        
        print(f"📊 Successfully processed {len(memories)} out of {len(memories_data)} conversations")
        
        if memories:
            # Analyze the conversation string representation
            from database.users import get_people_by_ids
            from models.other import Person
            
            # Get people involved in conversations
            all_person_ids = []
            for m in memories:
                all_person_ids.extend([s.person_id for s in m.transcript_segments if s.person_id])
            
            people = []
            if all_person_ids:
                people_data = get_people_by_ids(user_id, list(set(all_person_ids)))
                people = [Person(**p) for p in people_data]
                print(f"👥 Found {len(people)} people in conversations")
            
            # Generate conversation string (same as used in LLM prompt)
            conversation_history = Conversation.conversations_to_string(memories, people=people)
            print(f"📝 Conversation history length: {len(conversation_history)} characters")
            print(f"📄 Conversation history preview:")
            print(f"   {conversation_history[:500]}...")
            
            return memories, conversation_history
        else:
            print("❌ No valid Conversation objects created")
            return None, None
            
    except Exception as e:
        print(f"❌ Error processing conversations: {e}")
        import traceback
        traceback.print_exc()
        return None, None


def analyze_llm_prompt_and_response(user_id: str, memories: list):
    """Analyze the LLM prompt and response generation"""
    print(f"\n🤖 Step 4: Analyzing LLM Prompt and Response")
    print("=" * 80)
    
    try:
        # Get the exact prompt that would be sent to LLM
        user_name, memories_str = get_prompt_memories(user_id)
        
        # Get people and conversation history
        all_person_ids = []
        for m in memories:
            all_person_ids.extend([s.person_id for s in m.transcript_segments if s.person_id])
        
        people = []
        if all_person_ids:
            from database.users import get_people_by_ids
            from models.other import Person
            people_data = get_people_by_ids(user_id, list(set(all_person_ids)))
            people = [Person(**p) for p in people_data]
        
        conversation_history = Conversation.conversations_to_string(memories, people=people)
        
        # Reconstruct the exact prompt
        prompt = f"""
You are an experienced mentor, that helps people achieve their goals and improve their lives.
You are advising {user_name} right now, {memories_str}

The following are a list of {user_name}'s conversations from today, with the transcripts and a slight summary of each, that {user_name} had during his day.
{user_name} wants to get a summary of the key action items {user_name} has to take based on today's conversations.

Remember {user_name} is busy so this has to be very efficient and concise.
Respond in at most 50 words.

Output your response in plain text, without markdown. No newline character and only use numbers for the action items.
```
{conversation_history}
```
""".replace('    ', '').strip()
        
        print(f"📝 LLM Prompt Analysis:")
        print(f"   - User name: {user_name}")
        print(f"   - Memory context length: {len(memories_str)} chars")
        print(f"   - Conversation history length: {len(conversation_history)} chars")
        print(f"   - Total prompt length: {len(prompt)} chars")
        
        print(f"\n📄 Full LLM Prompt:")
        print("=" * 50)
        print(prompt)
        print("=" * 50)
        
        # Generate the actual summary
        print(f"\n🎯 Generating LLM Response...")
        summary = get_conversation_summary(user_id, memories)
        
        print(f"✅ Generated Summary:")
        print(f"   Length: {len(summary)} characters")
        print(f"   Content: {summary}")
        
        return prompt, summary
        
    except Exception as e:
        print(f"❌ Error analyzing LLM prompt/response: {e}")
        import traceback
        traceback.print_exc()
        return None, None


def main():
    """Main analysis function"""
    user_id = "mcaK5709t3MZAcUpdAeEGrmYgaT2"
    
    print("🔍 Daily Summary Content Generation Analysis")
    print("=" * 80)
    print(f"🎯 Analyzing user: {user_id}")
    
    # Step 1: Analyze raw conversation data
    memories_data = analyze_conversation_data(user_id)
    if not memories_data:
        print("\n❌ Cannot proceed without conversation data")
        return
    
    # Step 2: Analyze memory context
    user_name, memories_str = analyze_memory_context(user_id)
    
    # Step 3: Process conversations
    memories, conversation_history = analyze_conversation_processing(user_id, memories_data)
    if not memories:
        print("\n❌ Cannot proceed without processed conversations")
        return
    
    # Step 4: Analyze LLM prompt and response
    prompt, summary = analyze_llm_prompt_and_response(user_id, memories)
    
    # Final analysis
    print(f"\n📋 Final Analysis")
    print("=" * 80)
    
    if summary:
        print(f"✅ Daily summary generation is working correctly")
        print(f"📝 The content IS generated from actual conversations")
        print(f"🤖 LLM is processing real conversation data and generating action items")
        print(f"📊 Summary type: Action items extracted from conversations")
        
        print(f"\n🔍 Content Analysis:")
        if "评估并推动Noah模型" in summary:
            print(f"   - Content appears to be work-related action items")
            print(f"   - Language: Chinese")
            print(f"   - Format: Numbered action items")
            print(f"   - Source: Real conversations about work tasks")
        
        print(f"\n💡 Explanation:")
        print(f"   The 'todo list' appearance is actually correct behavior!")
        print(f"   The system is designed to extract ACTION ITEMS from conversations,")
        print(f"   not provide a narrative summary of what was discussed.")
        print(f"   This is intentional - it's meant to help users with next steps.")
        
    else:
        print(f"❌ Daily summary generation failed")
        print(f"🔧 Check LLM configuration and conversation data")


if __name__ == "__main__":
    main()
