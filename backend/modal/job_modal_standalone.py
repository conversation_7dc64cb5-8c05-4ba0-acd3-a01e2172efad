import json
import os
import sys
import asyncio
import concurrent.futures
import math
import threading
import uuid
from datetime import datetime, time, timezone
from typing import List, Tuple

import firebase_admin
import pytz
from firebase_admin import messaging, firestore
from google.cloud.firestore_v1.base_query import FieldFilter

from modal import Image, App, Secret, Cron
from pydantic import BaseModel, Field
from typing import Optional

# Define the app
app = App(
    name='omi-notifications-standalone',
    secrets=[Secret.from_name("gcp-credentials"), Secret.from_name('envs')],
)

# Create the image with necessary dependencies
image = (
    Image.debian_slim()
    .apt_install('ffmpeg', 'git', 'unzip')
    .pip_install("pytz", "firebase-admin", "google-cloud-firestore", "pydantic", "langchain-core", "langchain-openai")
    .env({"PYTHONPATH": "/root"})
)


# Model classes needed for daily summary
class NotificationMessage(BaseModel):
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    created_at: str = Field(default_factory=lambda: datetime.now(tz=timezone.utc).isoformat())
    sender: str = Field(default='ai')
    plugin_id: Optional[str] = None
    from_integration: str
    type: str
    notification_type: str
    text: Optional[str] = ""
    navigate_to: Optional[str] = None

    @staticmethod
    def get_message_as_dict(message: 'NotificationMessage') -> dict:
        message_dict = message.dict()

        # Remove 'plugin_id' if it is None
        if message.plugin_id is None:
            del message_dict['plugin_id']

        if message.navigate_to is None:
            del message_dict['navigate_to']

        return message_dict


class MessageSender:
    ai = 'ai'
    human = 'human'


class MessageType:
    text = 'text'
    day_summary = 'day_summary'


class Message(BaseModel):
    id: str
    text: str
    created_at: datetime
    sender: str
    app_id: Optional[str] = None
    plugin_id: Optional[str] = None
    from_external_integration: bool = False
    type: str
    memories_id: List[str] = []
    reported: bool = False
    report_reason: Optional[str] = None
    files_id: List[str] = []
    chat_session_id: Optional[str] = None
    data_protection_level: Optional[str] = None


def add_summary_message(text: str, uid: str) -> Message:
    """Add a daily summary message to the user's chat"""
    db = firestore.client()
    ai_message = Message(
        id=str(uuid.uuid4()),
        text=text,
        created_at=datetime.now(timezone.utc),
        sender='ai',
        app_id=None,
        from_external_integration=False,
        type='day_summary',
        memories_id=[],
    )

    # Add message to Firebase
    user_ref = db.collection('users').document(uid)
    message_data = ai_message.dict()
    del message_data['memories_id']  # Remove memories field for storage
    user_ref.collection('messages').add(message_data)
    print(f'[DEBUG] Successfully added daily summary message to Firebase for user {uid}')
    return ai_message


def filter_conversations_by_date(uid: str, start_date: datetime, end_date: datetime):
    """Filter conversations by date range"""
    db = firestore.client()
    user_ref = db.collection('users').document(uid)
    query = (
        user_ref.collection('conversations')
        .where(filter=FieldFilter('created_at', '>=', start_date))
        .where(filter=FieldFilter('created_at', '<=', end_date))
        .where(filter=FieldFilter('discarded', '==', False))
        .order_by('created_at', direction=firestore.Query.DESCENDING)
    )
    conversations = [doc.to_dict() for doc in query.stream()]
    return conversations


def get_conversation_summary(uid: str, memories: List[dict]) -> str:
    """Generate a simple daily summary from conversations"""
    if not memories:
        return "No conversations found for today."

    # Simple summary generation - in production this would use LLM
    conversation_count = len(memories)
    total_segments = sum(len(memory.get('transcript_segments', [])) for memory in memories)

    summary_parts = [
        f"📊 Daily Summary for {datetime.now().strftime('%B %d, %Y')}",
        f"• {conversation_count} conversation{'s' if conversation_count != 1 else ''} recorded",
        f"• {total_segments} total segments captured",
    ]

    # Add conversation titles if available
    if conversation_count > 0:
        summary_parts.append("\n🗣️ Conversations:")
        for i, memory in enumerate(memories[:3], 1):  # Show up to 3 conversations
            title = memory.get('structured', {}).get('title', f'Conversation {i}')
            summary_parts.append(f"  {i}. {title}")

        if conversation_count > 3:
            summary_parts.append(f"  ... and {conversation_count - 3} more")

    summary_parts.append("\n💡 Tap this notification to view your full daily summary in the app!")

    return "\n".join(summary_parts)


def should_run_job():
    """Check if we should run the job based on current time in different timezones"""
    current_utc = datetime.now(pytz.utc)
    target_hours = {8, 22}  # 8 AM and 10 PM
    print(f'should_run_job: Current UTC time: {current_utc}')

    matching_timezones = []
    for tz in pytz.all_timezones:
        try:
            local_time = current_utc.astimezone(pytz.timezone(tz))
            if local_time.hour in target_hours and local_time.minute == 0:
                matching_timezones.append(f"{tz} ({local_time.strftime('%H:%M')})")
        except Exception:
            # Skip invalid timezones
            continue

    if matching_timezones:
        print(f'should_run_job: Found {len(matching_timezones)} timezones at target hours: {matching_timezones[:5]}...')
        return True

    print('should_run_job: No timezones found at target hours (8 AM or 10 PM)')
    return False


def send_notification(token: str, title: str, body: str, data: dict = None):
    """Send a single notification via Firebase"""
    print(f'send_notification to token: {token[:20]}...')
    notification = messaging.Notification(title=title, body=body)
    message = messaging.Message(notification=notification, token=token)

    if data:
        message.data = data

    try:
        response = messaging.send(message)
        print('send_notification success:', response)
        return True
    except Exception as e:
        error_message = str(e)
        if "Requested entity was not found" in error_message:
            print(f'Token not found, should remove: {token[:20]}...')
        print('send_notification failed:', e)
        return False


async def send_bulk_notification(user_tokens: list, title: str, body: str):
    """Send bulk notifications with batching"""
    try:
        batch_size = 500
        num_batches = math.ceil(len(user_tokens) / batch_size)
        print(f'Sending bulk notifications to {len(user_tokens)} users in {num_batches} batches')

        def send_batch(batch_users):
            messages = [
                messaging.Message(notification=messaging.Notification(title=title, body=body), token=token)
                for token in batch_users
            ]
            return messaging.send_each(messages)

        tasks = []
        for i in range(num_batches):
            start = i * batch_size
            end = start + batch_size
            batch_users = user_tokens[start:end]
            task = asyncio.to_thread(send_batch, batch_users)
            tasks.append(task)

        await asyncio.gather(*tasks)
        print(f'Bulk notification sending completed for {len(user_tokens)} users')

    except Exception as e:
        print("Error sending bulk notifications:", e)


def get_timezones_at_time(target_time: str):
    """Get all timezones currently at the target time"""
    target_timezones = []
    for tz_name in pytz.all_timezones:
        try:
            tz = pytz.timezone(tz_name)
            current_time = datetime.now(tz).strftime("%H:%M")
            if current_time == target_time:
                target_timezones.append(tz_name)
        except Exception:
            continue
    return target_timezones


async def get_users_token_in_timezones(timezones: List[str]):
    """Get FCM tokens for users in specific timezones"""
    if not timezones:
        return []
    
    db = firestore.client()
    users_ref = db.collection('users')
    user_tokens = []
    
    # Split timezones into chunks of 30 (Firestore 'in' query limit)
    timezone_chunks = [timezones[i : i + 30] for i in range(0, len(timezones), 30)]
    
    async def query_chunk(chunk):
        def sync_query():
            chunk_tokens = []
            try:
                query = users_ref.where(filter=FieldFilter('time_zone', 'in', chunk))
                for doc in query.stream():
                    doc_data = doc.to_dict()
                    if 'fcm_token' in doc_data and doc_data['fcm_token']:
                        chunk_tokens.append(doc_data['fcm_token'])
            except Exception as e:
                print(f"Error querying chunk {chunk}: {e}")
            return chunk_tokens
        
        return await asyncio.to_thread(sync_query)
    
    tasks = [query_chunk(chunk) for chunk in timezone_chunks]
    results = await asyncio.gather(*tasks)
    
    for chunk_tokens in results:
        user_tokens.extend(chunk_tokens)
    
    return user_tokens


async def get_users_id_in_timezones(timezones: List[str]):
    """Get user IDs and FCM tokens for users in specific timezones"""
    if not timezones:
        return []

    db = firestore.client()
    users_ref = db.collection('users')
    user_data = []

    # Split timezones into chunks of 30 (Firestore 'in' query limit)
    timezone_chunks = [timezones[i : i + 30] for i in range(0, len(timezones), 30)]

    async def query_chunk(chunk):
        def sync_query():
            chunk_data = []
            try:
                query = users_ref.where(filter=FieldFilter('time_zone', 'in', chunk))
                for doc in query.stream():
                    doc_data = doc.to_dict()
                    if 'fcm_token' in doc_data and doc_data['fcm_token']:
                        chunk_data.append((doc.id, doc_data['fcm_token']))
            except Exception as e:
                print(f"Error querying chunk {chunk}: {e}")
            return chunk_data

        return await asyncio.to_thread(sync_query)

    tasks = [query_chunk(chunk) for chunk in timezone_chunks]
    results = await asyncio.gather(*tasks)

    for chunk_data in results:
        user_data.extend(chunk_data)

    return user_data


async def send_daily_notification():
    """Send morning notification at 8 AM"""
    try:
        print("Starting send_daily_notification...")
        morning_alert_title = "Memorion"
        morning_alert_body = "Wear your Memorion device to capture your conversations today."
        morning_target_time = "08:00"
        
        timezones_in_time = get_timezones_at_time(morning_target_time)
        user_tokens = await get_users_token_in_timezones(timezones_in_time)
        
        if not user_tokens:
            print("No users found for morning notification")
            return None
            
        await send_bulk_notification(user_tokens, morning_alert_title, morning_alert_body)
        print(f"send_daily_notification completed. Users notified: {len(user_tokens)}")
        return user_tokens
        
    except Exception as e:
        print(f"Error in send_daily_notification: {e}")
        import traceback
        traceback.print_exc()
        return None


def _send_summary_notification(user_data: tuple):
    """Send individual daily summary notification with content and navigation"""
    uid = user_data[0]
    fcm_token = user_data[1]
    daily_summary_title = "Here is your action plan for tomorrow"

    try:
        # Get today's conversations for the user
        memories_data = filter_conversations_by_date(
            uid, datetime.combine(datetime.now().date(), time.min), datetime.now()
        )

        if not memories_data:
            print(f"No conversations found for user {uid}, skipping notification")
            return

        # Generate summary content
        summary = get_conversation_summary(uid, memories_data)

        # Create notification message with navigation data
        ai_message = NotificationMessage(
            text=summary,
            from_integration='false',
            type='day_summary',
            notification_type='daily_summary',
            navigate_to="/chat/omi",  # This enables navigation to daily summary
        )

        # Store the summary in the user's chat
        add_summary_message(summary, uid)

        # Send notification with navigation payload
        send_notification(fcm_token, daily_summary_title, summary, NotificationMessage.get_message_as_dict(ai_message))
        print(f"Daily summary notification sent to user {uid}")

    except Exception as e:
        print(f"Error sending summary notification to user {uid}: {e}")


async def _send_bulk_summary_notification(users: list):
    """Send daily summary notifications to multiple users"""
    loop = asyncio.get_running_loop()
    with concurrent.futures.ThreadPoolExecutor() as pool:
        tasks = [loop.run_in_executor(pool, _send_summary_notification, user_data) for user_data in users]
        await asyncio.gather(*tasks)


async def send_daily_summary_notification():
    """Send evening summary notification at 10 PM with proper navigation"""
    try:
        print("Starting send_daily_summary_notification...")
        daily_summary_target_time = "22:00"

        timezones_in_time = get_timezones_at_time(daily_summary_target_time)
        user_data = await get_users_id_in_timezones(timezones_in_time)

        if not user_data:
            print("No users found for daily summary notification")
            return None

        print(f"Found {len(user_data)} users for daily summary notification")
        await _send_bulk_summary_notification(user_data)
        print(f"send_daily_summary_notification completed. Users notified: {len(user_data)}")
        return user_data

    except Exception as e:
        print(f"Error in send_daily_summary_notification: {e}")
        import traceback
        traceback.print_exc()
        return None


async def start_cron_job():
    """Main cron job function"""
    print(f'start_cron_job called at {datetime.now(pytz.utc)}')
    if should_run_job():
        print('start_cron_job: should_run_job returned True, sending notifications...')
        try:
            await send_daily_notification()
            await send_daily_summary_notification()
            print('start_cron_job: All notifications sent successfully')
        except Exception as e:
            print(f'Error in start_cron_job: {e}')
            import traceback
            traceback.print_exc()
    else:
        print('start_cron_job: should_run_job returned False, no notifications to send')


@app.function(
    image=image,
    schedule=Cron('* * * * *'),
    timeout=300,
    memory=512,
)
async def notifications_cronjob():
    """Scheduled notification cron job"""
    print(f'Notification cron job started at {datetime.now(pytz.utc)}')
    
    # Initialize Firebase
    try:
        if os.environ.get('SERVICE_ACCOUNT_JSON'):
            service_account_info = json.loads(os.environ["SERVICE_ACCOUNT_JSON"])
            credentials = firebase_admin.credentials.Certificate(service_account_info)
            firebase_admin.initialize_app(credentials)
            print('Firebase initialized with service account')
        else:
            firebase_admin.initialize_app()
            print('Firebase initialized with default credentials')
    except Exception as e:
        print(f'Firebase initialization error: {e}')
        return {"status": "error", "message": f"Firebase init failed: {e}"}
    
    try:
        await start_cron_job()
        return {"status": "success", "message": "Cron job completed", "ran_at": str(datetime.now(pytz.utc))}
    except Exception as e:
        print(f'Error in notifications_cronjob: {e}')
        import traceback
        traceback.print_exc()
        return {"status": "error", "message": str(e), "ran_at": str(datetime.now(pytz.utc))}


@app.function(
    image=image,
    timeout=300,
    memory=512,
)
async def manual_notification_trigger():
    """Manual trigger for testing notifications"""
    print(f'Manual notification trigger started at {datetime.now(pytz.utc)}')
    
    # Initialize Firebase
    try:
        if os.environ.get('SERVICE_ACCOUNT_JSON'):
            service_account_info = json.loads(os.environ["SERVICE_ACCOUNT_JSON"])
            credentials = firebase_admin.credentials.Certificate(service_account_info)
            firebase_admin.initialize_app(credentials)
            print('Firebase initialized with service account')
        else:
            firebase_admin.initialize_app()
            print('Firebase initialized with default credentials')
    except Exception as e:
        print(f'Firebase initialization error: {e}')
        return {"status": "error", "message": f"Firebase init failed: {e}"}
    
    try:
        await start_cron_job()
        print('Manual notification trigger completed successfully')
        return {"status": "success", "message": "Manual notifications completed", "ran_at": str(datetime.now(pytz.utc))}
    except Exception as e:
        print(f'Error in manual notification trigger: {e}')
        import traceback
        traceback.print_exc()
        return {"status": "error", "message": str(e), "ran_at": str(datetime.now(pytz.utc))}
